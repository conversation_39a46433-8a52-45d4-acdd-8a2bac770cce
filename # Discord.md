# Discord.js UI Implementation
- Use Discord.js Components v2 for UI elements with proper constraints: cannot use 'embeds'+'content' or 'content'+'components' simultaneously in the same message.
- Discord.js Components v2 has a constraint where the 'embeds' field cannot be used together with MessageFlags.IS_COMPONENTS_V2.
- Only add a section if it contains a button or thumbnail component to prevent errors; use MediaGalleryBuilder for media components.
- For commands with buttons, use deferUpdate() to prevent 'unknown interaction' errors when response times might be slow.
- For pagination, edit existing messages rather than sending new responses when users click navigation buttons.
- When using editOrReply method, use the separate flags parameter rather than including flags in the options object.
- Discord.js has a limit of 40 total UI components per message, which must be considered when implementing paginated interfaces.
- User prefers prettier layouts with select menus inside containers for better user-friendliness, and having select menus directly on first page of multi-step interfaces.
- Example implementations can be found in src/commands/Information/about.ts and the /help command.

# Command Structure and Management
- For command files, use folder structure to create subcommands like in other commands.
- Enhance <PERSON>Command to store command directory structure metadata during loading to eliminate filesystem operations in help commands.
- The most used InterChat commands in order are: /call, /hangup, /connect, /deletemsg, /disconnect, /editmsg, /setup, /about, /skip, and /leaderboard.
- User prefers /call and /hangup commands to be less bulky and more streamlined; don't ask for confirmation in hangup command.
- For hangup command, provide different responses when used in queue versus in active call.
- In /call command, make the leaderboard button open the calls leaderboard (same as in /leaderboard calls).

# Setup Command Requirements
- User wants a new setup command with step-by-step onboarding using Discord.js Components v2, language selection, progress indicators, intuitive navigation, and professional UI.
- User rates the current setup command poorly (3/10) and reports that language selection doesn't work.
- User prefers setup command to have more interactivity and 'wow' moments in the UI/UX design.

# Help Command Improvements
- User wants help command refactored to be more maintainable with smaller functions, less duplication, better type safety, proper Components v2 usage.
- Help command should preserve all existing functionality including autocomplete, pagination, and button navigation.
- Use chatInputApplicationCommandMention() to create proper clickable command mentions rather than plain text command names.
- Help command autocomplete should filter commands and categories based on user input, sort results alphabetically, and limit results (max 25 items).

# Feature Implementation and Prioritization
- The call feature is negatively impacting the hub feature, which is a critical issue requiring attention.
- User wants to migrate message data storage from Redis to PostgreSQL, with a scheduled job running every 12 hours to delete messages older than 24 hours.
- User wants a premium subscription system with multiple tiers starting from $1.99/month, focusing on valuable features for text communication, moderation, and community management.
- InterChat uses Ko-fi for premium subscription payments rather than other payment platforms.
- Premium features should respect Discord's actual limitations (10MB max file uploads, 2000/4000 character message limits) and focus on substantial utility features.
- Users should be able to review tutorial content after completion.
- InterChat requires a content filter for calls that blocks prohibited content (slurs, explicit terms, hate speech, threats), notifies recipients with humorous remarks when messages are blocked, stores filter lists in a separate config file, and logs blocked messages for moderation.
- InterChat may need an events/onMessageEdit handler to properly handle message edit events across the system.
- User is interested in improving the hubs feature to enhance community building aspects of InterChat.
- InterChat needs an achievement system with 26 specific achievements tracking metrics like message counts, hub participation, and moderation activities, designed to be modular, efficient, user-friendly, and visually appealing with badges.

# Context and Type Safety
- Make the Context class more robust and easier to maintain/extend to avoid type errors with Discord.js updates.
- For ComponentContext, ensure guildId is valid by using proper type guards or assertions rather than runtime checks.
- When implementing TypeScript type guards, use type predicates to properly narrow interaction types for type-safe access to properties.
- When using the inGuild() type guard, guild, guildId, and member properties should be guaranteed to be non-null.
- User prefers type-safe approaches with proper encapsulation (getters/setters) over direct property mutation with @ts-expect-error comments.

# Performance and Caching
- InterChat runs on 5GB of RAM total with 9 shards (3 clusters), requiring careful memory cache sizing and cross-shard invalidation.
- User prioritizes optimizing message processing performance, particularly fetchUserData (~92ms), checkBanAndBlacklist (~155ms) to achieve under 100ms total processing time.
- When implementing caching, ensure cache invalidation occurs when related data is modified to prevent inconsistencies between database and cache.
- Use Redis scan command instead of keys command for better performance when invalidating cache.
- Application startup memory usage has doubled from 25MB to 50MB after recent changes, requiring investigation and optimization.
- Redis caching for hub connections needs cross-channel cache invalidation to prevent stale data when hub membership changes.
- User prefers considering two-tier caching strategies with separate cache keys for entities and their relationships to reduce cross-invalidation needs while maintaining data consistency.
- InterChat's most active hubs have around 15-300 connections.

# Release Announcements
- User prefers release announcements to include visual recommendations, narrative-driven introductions, and comprehensive blog posts with feature descriptions, user benefits, and implementation guidance.