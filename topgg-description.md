# InterChat: Connect Discord Communities Seamlessly

InterChat breaks down the walls between Discord servers, enabling real-time cross-server communication that builds thriving, engaged communities around shared interests.

## ✨ Standout Features

### 🌐 Powerful Hub System

Create or join themed communities where messages flow naturally between servers. Whether you're building a gaming network, educational community, or hobby group, InterChat hubs provide the infrastructure for meaningful cross-server connections.

### 📞 Direct Server Calls

Need a quick connection between just two servers? Our call feature lets you instantly connect channels for temporary collaborations without the commitment of joining a hub.

### 🛡️ Advanced Security & Moderation

Keep your cross-server conversations safe with smart content filtering, anti-spam protection, and comprehensive moderation tools that work across server boundaries.

### 🎮 Visual Dashboard

Manage your communities through our intuitive web interface. Create and configure hubs, track analytics, and monitor server connections all in one place.

### 🔧 Highly Customizable

Personalize your experience with custom welcome messages, hub rules, and flexible permission settings that put you in control of your community.

## 🚀 Getting Started

1. **Add InterChat to your server**: Click the "Invite" button above
2. **Run the setup wizard**: Type `/setup` to configure your first connection
    - **Or Manually Connect your channel**: Use `/connect` to link your channel to a hub
3. **Start chatting!** Messages will now flow between all servers in the hub

## ✨ Premium Features

[Vote for us](https://top.gg/bot/769921109209907241/vote) every 12 hours to unlock voter perks including increased message length, sticker support, and custom welcome messages!

## 🔗 Useful Links

- [Official Website](https://interchat.tech)
- [Documentation](https://interchat.tech/docs)
- [Support Server](https://discord.gg/cgYgC6YZyX)
- [Ko-fi Page](https://ko-fi.com/interchat)

Join thousands of communities already using InterChat to build connections that transcend server boundaries!

<style>
:root {
    --dark-bg-1: #1a1b1e;
    --dark-bg-2: #2b2d31;
    --dark-bg-3: #232428;
    --accent: #9172D8;
    --accent-dark: #6b46c1;
    --top-gg-colors-brand-100: #9172D8;
    --top-gg-colors-brand-80: #B794F4;
    --top-gg-colors-brand-60: #9F7AEA;
    --top-gg-colors-brand-40: #805AD5;
    --top-gg-colors-brand-20: #6B46C1;
    --top-gg-colors-brand-15: #553C9A;
    --top-gg-colors-brand-10: #44337A;
    --top-gg-colors-brand-5: #322659;
    --color-brand-20: #44337A;
}

body {
    background: linear-gradient(
        135deg,
        var(--dark-bg-1) 0%,
        var(--dark-bg-2) 85%,
        var(--dark-bg-3) 100%
    );
    color: #ffffff;
    min-height: 100vh;
    margin: 0;
    padding: 20px;
}

iframe {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2);
    background: var(--dark-bg-2);
}

a {
    color: var(--accent);
    text-decoration: none;
}

a:hover {
    color: var(--accent-dark);
    text-decoration: underline;
}
</style>

# Welcome to InterChat

InterChat isn't just a bot—it's a revolutionary bridge connecting Discord communities worldwide. Whether you're aiming to spark new conversations, expand your server's reach, or keep your members engaged, InterChat makes it happen effortlessly.

<iframe src="https://interchat.tech" width="100%" height="600px" frameborder="0" style="border-radius: 10px; margin: 20px 0;"></iframe>

## What Makes InterChat Special?

### 💬 **Cross-Server Chatting**

Break down the walls of isolated conversations! InterChat enables your server's channels to connect to hubs—shared spaces where multiple servers engage in real-time discussions. Messages sent in a linked channel instantly appear across all connected servers, creating a vibrant, interconnected experience.

### 📞 **NEW: `/call` - Cross-Server Text Connections**

Connect with other servers using our new **/call** feature! This matchmaking system pairs your server's text channel with another random server for a temporary chat session. Here's how it works:

- Use `/call` to join the matchmaking queue
- Get matched with another server that's also looking to connect
- Chat freely between both servers through our webhook system
- Use `/hangup` to end the call or `/skip` to find a new match
- Rate your experience after each call, gaining reputation points for future matches
- Track your participation on `/leaderboard calls`

Each successful call contributes to both user and server leaderboards, helping you discover and connect with new communities!

### 🛡️ **Robust Moderation Tools**

Maintaining a safe community is paramount. InterChat offers built-in moderation features:

- **Anti-Swear & Custom Filters**: Block unwanted words or patterns
- **Message Logging**: Keep track of deleted messages, reports, and moderation actions
- **Blacklist System**: Prevent problematic users or servers from rejoining
- **Report & Appeal System**: Community-driven tools to handle issues efficiently

Learn more about our moderation capabilities at [interchat.tech](https://www.interchat.tech).

### 🌟 **Leaderboards & Achievements**

Showcase your server's activity with our leaderboard system, highlighting the most active and engaging communities, and rewarding participation with ranks and recognition.

### 🎨 **Fully Customizable Hubs**

Personalize your hub with:

- **Custom Icons, Banners & Description**: Reflect your community's identity
- **Public or Private Access Control**: Manage who joins your hub
- **Custom Rules & Filters**: Set the tone and guidelines for interactions

Explore customization options at [interchat.tech](https://www.interchat.tech).

### 🌐 **Easy Discovery & Growth**

Expand your community's reach:

- **Public Hub Listings**: Browse and join active communities
- **Server Exposure**: Gain visibility by engaging with a wider audience

Discover public hubs at [interchat.tech/hubs](https://www.interchat.tech/hubs).

<iframe src="https://interchat.tech/hubs" width="100%" height="800px" frameborder="0" style="border-radius: 10px; margin: 20px 0;"></iframe>

## 🔄 How to Get Started

### **Setup InterChat**

1. Run `/setup` in a new or dedicated channel
2. Click "Join a Hub" to find an active public hub or "Create a Hub" to make your own
3. Select a channel to link
4. Start chatting—your messages will appear across all connected servers!

**Recommended:** Join the **InterChat Central** hub for an instant chat experience.

For detailed setup guides, visit our [documentation](https://www.interchat.tech).

## 🔍 Discover More Features

<iframe src="https://www.interchat.tech/docs" width="100%" height="600px" frameborder="0" style="border-radius: 10px; margin: 20px 0;"></iframe>

- **Leaderboards**: Compete for the top spot!
- **Hubs**: Learn how to manage and grow your hub
- **Moderation**: Keep your community safe with advanced tools
- **Public Hub Finder**: Explore thriving communities

Explore these features at [interchat.tech](https://www.interchat.tech).

## 📝 Need Help? Have Suggestions?

- **Join Our Support Server**: [Click Here](https://interchat.tech/support)
- **Report Bugs or Request Features**: [GitHub](https://github.com/interchatapp/InterChat)
- **Browse Public Hubs**: [Find Active Communities](https://www.interchat.tech/hubs)

InterChat is the future of cross-server communication. **Join today and start connecting!**

<style>
:root {
    --dark-bg-1: #1a1b1e;
    --dark-bg-2: #2b2d31;
    --dark-bg-3: #232428;
    --accent: #9172D8;
    --accent-dark: #6b46c1;
    --top-gg-colors-brand-100: #9172D8;
    --top-gg-colors-brand-80: #B794F4;
    --top-gg-colors-brand-60: #9F7AEA;
    --top-gg-colors-brand-40: #805AD5;
    --top-gg-colors-brand-20: #6B46C1;
    --top-gg-colors-brand-15: #553C9A;
    --top-gg-colors-brand-10: #44337A;
    --top-gg-colors-brand-5: #322659;
    --color-brand-20: #44337A;
}

body {
    background: linear-gradient(
        135deg,
        var(--dark-bg-1) 0%,
        var(--dark-bg-2) 85%,
        var(--dark-bg-3) 100%
    );
    color: #ffffff;
    min-height: 100vh;
    margin: 0;
    padding: 20px;
}

iframe {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2);
    background: var(--dark-bg-2);
}

a {
    color: var(--accent);
    text-decoration: none;
}

a:hover {
    color: var(--accent-dark);
    text-decoration: underline;
}

        .bot-description {
            background: linear-gradient(135deg, #1e1e2e 0%, #2d2d44 50%, #1a1a2e 100%);
            color: #ffffff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 20px 60px rgba(145, 114, 216, 0.3);
            position: relative;
            overflow: hidden;
            animation: glow 3s ease-in-out infinite alternate;
        }

        /* Epic glow animation */
        @keyframes glow {
            from { box-shadow: 0 20px 60px rgba(145, 114, 216, 0.3); }
            to { box-shadow: 0 25px 80px rgba(145, 114, 216, 0.5); }
        }

        /* Animated background particles */
        .bot-description::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 20%, rgba(145, 114, 216, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(145, 114, 216, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(145, 114, 216, 0.05) 0%, transparent 50%);
            animation: float 6s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(2deg); }
        }

        /* Headers with epic styling */
        h1 {
            background: linear-gradient(45deg, #9172D8, #B794F6, #E879F9);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2.5em;
            font-weight: 900;
            text-align: center;
            margin-bottom: 1rem;
            text-shadow: 0 0 30px rgba(145, 114, 216, 0.5);
            animation: pulse 2s ease-in-out infinite alternate;
            position: relative;
            z-index: 2;
        }

        @keyframes pulse {
            from { text-shadow: 0 0 30px rgba(145, 114, 216, 0.5); }
            to { text-shadow: 0 0 50px rgba(145, 114, 216, 0.8); }
        }

        h2 {
            color: #9172D8;
            font-size: 1.8em;
            font-weight: 700;
            margin: 2rem 0 1rem 0;
            position: relative;
            text-transform: uppercase;
            letter-spacing: 2px;
            z-index: 2;
        }

        h2::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #9172D8, transparent);
            border-radius: 2px;
            animation: expand 2s ease-in-out infinite alternate;
        }

        @keyframes expand {
            from { width: 60px; }
            to { width: 120px; }
        }

        h3 {
            color: #B794F6;
            font-size: 1.4em;
            font-weight: 600;
            margin: 1.5rem 0 0.8rem 0;
            position: relative;
            z-index: 2;
        }

        /* Epic feature boxes */
        .feature-section {
            background: rgba(145, 114, 216, 0.1);
            border: 2px solid rgba(145, 114, 216, 0.3);
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
        }

        .feature-section:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(145, 114, 216, 0.4);
            border-color: #9172D8;
        }

        /* Epic bullet points */
        ul {
            list-style: none;
            padding-left: 0;
            position: relative;
            z-index: 2;
        }

        li {
            position: relative;
            padding-left: 2rem;
            margin: 0.8rem 0;
            transition: all 0.3s ease;
        }

        li::before {
            content: '⚡';
            position: absolute;
            left: 0;
            color: #9172D8;
            font-size: 1.2em;
            animation: spark 1.5s ease-in-out infinite alternate;
        }

        @keyframes spark {
            from { transform: scale(1) rotate(0deg); }
            to { transform: scale(1.2) rotate(10deg); }
        }

        li:hover {
            color: #B794F6;
            transform: translateX(10px);
        }

        /* Epic emphasis boxes */
        .emphasis-box {
            background: linear-gradient(135deg, rgba(145, 114, 216, 0.2), rgba(183, 148, 246, 0.1));
            border-left: 4px solid #9172D8;
            padding: 1.2rem;
            margin: 1.5rem 0;
            border-radius: 8px;
            font-weight: 600;
            box-shadow: 0 8px 25px rgba(145, 114, 216, 0.2);
            position: relative;
            z-index: 2;
        }

        /* Epic call-to-action button styling */
        .cta-section {
            text-align: center;
            margin: 2rem 0;
            padding: 2rem;
            background: linear-gradient(135deg, rgba(145, 114, 216, 0.15), rgba(183, 148, 246, 0.1));
            border-radius: 15px;
            border: 2px solid rgba(145, 114, 216, 0.3);
            position: relative;
            z-index: 2;
        }

        .epic-button {
            display: inline-block;
            background: linear-gradient(45deg, #9172D8, #B794F6);
            color: white;
            padding: 1rem 2rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: bold;
            font-size: 1.1em;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 10px 30px rgba(145, 114, 216, 0.4);
            transition: all 0.3s ease;
            margin: 0.5rem;
            position: relative;
            overflow: hidden;
        }

        .epic-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .epic-button:hover::before {
            left: 100%;
        }

        .epic-button:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 15px 40px rgba(145, 114, 216, 0.6);
        }

        /* Premium features highlight */
        .premium-highlight {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: bold;
            text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
            animation: shimmer 2s ease-in-out infinite alternate;
        }

        @keyframes shimmer {
            from { opacity: 0.8; }
            to { opacity: 1; }
        }

        /* Strong text styling */
        strong {
            color: #9172D8;
            font-weight: 700;
        }

        /* Paragraph spacing and styling */
        p {
            margin: 1rem 0;
            position: relative;
            z-index: 2;
        }

        /* Mobile responsiveness */
        @media (max-width: 768px) {
            .bot-description {
                padding: 1.5rem;
                margin: 1rem;
            }

            h1 {
                font-size: 2em;
            }

            h2 {
                font-size: 1.5em;
            }

            .epic-button {
                padding: 0.8rem 1.5rem;
                font-size: 1em;
            }
        }

        /* Epic scrollbar */
        ::-webkit-scrollbar {
            width: 12px;
        }

        ::-webkit-scrollbar-track {
            background: #1e1e2e;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(180deg, #9172D8, #B794F6);
            border-radius: 6px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(180deg, #B794F6, #E879F9);
        }
</style>

# InterChat: Where Discord Communities Unite

## The Problem Every Growing Community Faces

Your Discord community is thriving, but you've hit a wall. Server member limits, multiple related servers, or the need to connect with allied communities – Discord's one-server-at-a-time design holds back your community's true potential.

## The Solution: Seamless Cross-Server Communication

InterChat eliminates Discord server boundaries by creating live communication bridges between servers. Your members can chat naturally across multiple servers as if they were all in the same place, without leaving their home server or managing multiple memberships.

## How InterChat Works

### Community Hubs: Long-Term Connections

Perfect for permanent partnerships and large networks. Create or join themed hubs where multiple servers share ongoing conversations:

- **Gaming Networks**: Connect your clan's recruitment server, competitive team server, and casual gaming server
- **Educational Communities**: Link university study groups, subject-specific servers, and tutoring communities  
- **Creator Collectives**: Unite art servers, writing communities, and content creator support groups
- **Business Ecosystems**: Connect company servers, partner organizations, and industry communities

### Server Calls: Quick Social Connections  

Perfect for spontaneous conversations and meeting new people. Instantly connect with another server for:

- Finding active chat partners when your server is quiet
- Breaking the ice with new communities
- Casual conversations and making new friends
- Discovering servers with similar interests

## Why Communities Choose InterChat

**Effortless Growth**: Scale your community across multiple servers without fragmenting your user base or forcing members to join multiple servers.

**Maintained Identity**: Each server keeps its unique culture, rules, and moderation while participating in larger conversations.

**Security**: Advanced spam filtering, content moderation, and permission controls work seamlessly across all connected servers.

**Complete Control**: Comprehensive admin dashboard lets you manage connections, monitor activity, and customize settings from one central location.

## Real Results for Real Communities

Thousands of Discord communities already use InterChat to break through server limitations and build stronger, more connected member experiences. From 50-member hobby groups to networks spanning dozens of servers, InterChat scales with your community's ambitions.

## Start Connecting Today

Ready to unlock your community's potential? Getting started takes less than 2 minutes:

1. **[Invite InterChat](https://discord.com/api/oauth2/authorize?client_id=769921109209907241&permissions=2214196311&scope=bot)** to your server
2. **Run `/setup`** for guided configuration, or **use `/connect`** to join your first hub  
3. **Watch your community grow** as conversations flow seamlessly across server boundaries

**[Add to Discord](https://discord.com/api/oauth2/authorize?client_id=769921109209907241&permissions=2214196311&scope=bot)** | **[View Documentation](https://interchat.tech/docs)** | **[Join Support Server](https://discord.gg/cgYgC6YZyX)**

*Transform your Discord community today. Your members will thank you for it.*

---

    <style>        
        /* Main container styling */
        .bot-description {
            background: linear-gradient(135deg, #1e1e2e 0%, #2d2d44 50%, #1a1a2e 100%);
            color: #ffffff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 20px 60px rgba(145, 114, 216, 0.3);
            position: relative;
            overflow: hidden;
            animation: glow 3s ease-in-out infinite alternate;
        }
        
        /* Epic glow animation */
        @keyframes glow {
            from { box-shadow: 0 20px 60px rgba(145, 114, 216, 0.3); }
            to { box-shadow: 0 25px 80px rgba(145, 114, 216, 0.5); }
        }
        
        /* Animated background particles */
        .bot-description::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 20%, rgba(145, 114, 216, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(145, 114, 216, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(145, 114, 216, 0.05) 0%, transparent 50%);
            animation: float 6s ease-in-out infinite;
            pointer-events: none;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(2deg); }
        }
        
        /* Headers with epic styling */
        h1 {
            background: linear-gradient(45deg, #9172D8, #B794F6, #E879F9);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2.5em;
            font-weight: 900;
            text-align: center;
            margin-bottom: 1rem;
            text-shadow: 0 0 30px rgba(145, 114, 216, 0.5);
            animation: pulse 2s ease-in-out infinite alternate;
            position: relative;
            z-index: 2;
        }
        
        @keyframes pulse {
            from { text-shadow: 0 0 30px rgba(145, 114, 216, 0.5); }
            to { text-shadow: 0 0 50px rgba(145, 114, 216, 0.8); }
        }
        
        h2 {
            color: #9172D8;
            font-size: 1.8em;
            font-weight: 700;
            margin: 2rem 0 1rem 0;
            position: relative;
            text-transform: uppercase;
            letter-spacing: 2px;
            z-index: 2;
        }
        
        h2::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #9172D8, transparent);
            border-radius: 2px;
            animation: expand 2s ease-in-out infinite alternate;
        }
        
        @keyframes expand {
            from { width: 60px; }
            to { width: 120px; }
        }
        
        h3 {
            color: #B794F6;
            font-size: 1.4em;
            font-weight: 600;
            margin: 1.5rem 0 0.8rem 0;
            position: relative;
            z-index: 2;
        }
        
        /* Epic feature boxes */

        .feature-section {
            background: rgba(145, 114, 216, 0.1);
            border: 2px solid rgba(145, 114, 216, 0.3);
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
        }
        
        .feature-section:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(145, 114, 216, 0.4);
            border-color: #9172D8;
        }
        
        ul {
            list-style: none;
            padding-left: 0;
            position: relative;
            z-index: 2;
        }
        
        li {
            position: relative;
            padding-left: 2rem;
            margin: 0.8rem 0;
            transition: all 0.3s ease;
        }
        
        li::before {
            content: '⚡';
            position: absolute;
            left: 0;
            color: #9172D8;
            font-size: 1.2em;
            animation: spark 1.5s ease-in-out infinite alternate;
        }
        
        @keyframes spark {
            from { transform: scale(1) rotate(0deg); }
            to { transform: scale(1.2) rotate(10deg); }
        }
        
        li:hover {
            color: #B794F6;
            transform: translateX(10px);
        }
        
        /* Epic emphasis boxes */
        .emphasis-box {
            background: linear-gradient(135deg, rgba(145, 114, 216, 0.2), rgba(183, 148, 246, 0.1));
            border-left: 4px solid #9172D8;
            padding: 1.2rem;
            margin: 1.5rem 0;
            border-radius: 8px;
            font-weight: 600;
            box-shadow: 0 8px 25px rgba(145, 114, 216, 0.2);
            position: relative;
            z-index: 2;
        }
        
        /* Epic call-to-action button styling */
        .cta-section {
            text-align: center;
            margin: 2rem 0;
            padding: 2rem;
            background: linear-gradient(135deg, rgba(145, 114, 216, 0.15), rgba(183, 148, 246, 0.1));
            border-radius: 15px;
            border: 2px solid rgba(145, 114, 216, 0.3);
            position: relative;
            z-index: 2;
        }
        
        .epic-button {
            display: inline-block;
            background: linear-gradient(45deg, #9172D8, #B794F6);
            color: white;
            padding: 1rem 2rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: bold;
            font-size: 1.1em;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 10px 30px rgba(145, 114, 216, 0.4);
            transition: all 0.3s ease;
            margin: 0.5rem;
            position: relative;
            overflow: hidden;
        }
        
        .epic-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .epic-button:hover::before {
            left: 100%;
        }
        
        .epic-button:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 15px 40px rgba(145, 114, 216, 0.6);
        }
        
        /* Premium features highlight */
        .premium-highlight {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: bold;
            text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
            animation: shimmer 2s ease-in-out infinite alternate;
        }
        
        @keyframes shimmer {
            from { opacity: 0.8; }
            to { opacity: 1; }
        }
        
        /* Strong text styling */
        strong {
            color: #9172D8;
            font-weight: 700;
        }
        
        /* Paragraph spacing and styling */
        p {
            margin: 1rem 0;
            position: relative;
            z-index: 2;
        }
        
        /* Mobile responsiveness */
        @media (max-width: 768px) {
            .bot-description {
                padding: 1.5rem;
                margin: 1rem;
            }
            
            h1 {
                font-size: 2em;
            }
            
            h2 {
                font-size: 1.5em;
            }
            
            .epic-button {
                padding: 0.8rem 1.5rem;
                font-size: 1em;
            }
        }
        
        /* Epic scrollbar */
        ::-webkit-scrollbar {
            width: 12px;
        }
        
        ::-webkit-scrollbar-track {
            background: #1e1e2e;
        }
        
        ::-webkit-scrollbar-thumb {
            background: linear-gradient(180deg, #9172D8, #B794F6);
            border-radius: 6px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(180deg, #B794F6, #E879F9);
        }
    </style>
<body>
    <div class="bot-description">
        <h1>InterChat: Where Discord Communities Unite</h1>

        <div class="feature-section">
            <h2>🚀 The Problem Every Growing Community Faces</h2>
            <p>Your Discord community is thriving, but you've hit a wall. Server member limits, multiple related servers, or the need to connect with allied communities – Discord's one-server-at-a-time design holds back your community's true potential.</p>
        </div>
        
        <div class="emphasis-box">
            <strong>The Solution: Seamless Cross-Server Communication</strong><br>
            InterChat eliminates Discord server boundaries by creating live communication bridges between servers. Your members can chat naturally across multiple servers as if they were all in the same place, without leaving their home server or managing multiple memberships.
        </div>
        
## ⚡ How InterChat Works

        <div class="feature-section">
            <h3>🌐 Community Hubs: Long-Term Connections</h3>
            <p>Perfect for permanent partnerships and large networks. Create or join themed hubs where multiple servers share ongoing conversations:</p>
            <ul>
                <li><strong>Gaming Networks</strong>: Connect your clan's recruitment server, competitive team server, and casual gaming server</li>
                <li><strong>Educational Communities</strong>: Link university study groups, subject-specific servers, and tutoring communities</li>
                <li><strong>Creator Collectives</strong>: Unite art servers, writing communities, and content creator support groups</li>
                <li><strong>Business Ecosystems</strong>: Connect company servers, partner organizations, and industry communities</li>
            </ul>
        </div>
        
        <div class="feature-section">
            <h3>📞 Server Calls: Quick Social Connections</h3>
            <p>Perfect for spontaneous conversations and meeting new people. Instantly connect with another server for:</p>
            <ul>
                <li>Finding active chat partners when your server is quiet</li>
                <li>Breaking the ice with new communities</li>
                <li>Casual conversations and making new friends</li>
                <li>Discovering servers with similar interests</li>
            </ul>
        </div>
        
        <h2>🎯 Why Communities Choose InterChat</h2>
        
        <div class="feature-section">
            <p><strong>Effortless Growth</strong>: Scale your community across multiple servers without fragmenting your user base or forcing members to join multiple servers.</p>
            
            <p><strong>Maintained Identity</strong>: Each server keeps its unique culture, rules, and moderation while participating in larger conversations.</p>
            
            <p><strong>Complete Control</strong>: Comprehensive admin dashboard lets you manage connections, monitor activity, and customize settings from one central location.</p>
        </div>
        
        <div class="feature-section">
            <h2 class="premium-highlight">✨ Premium Features</h2>
            <p>Vote for InterChat every 12 hours to unlock premium perks:</p>
            <ul>
                <li>Extended message length limits</li>
                <li>Custom sticker support</li>
                <li>Personalized welcome messages</li>
            </ul>
        </div>
        
        <div class="emphasis-box">
            <strong>Real Results for Real Communities</strong><br>
            Thousands of Discord communities already use InterChat to break through server limitations and build stronger, more connected member experiences. From 50-member hobby groups to networks spanning dozens of servers, InterChat scales with your community's ambitions.
        </div>
        
        <div class="cta-section">
            <h2>🚀 Start Connecting Today</h2>
            <p>Ready to unlock your community's potential? Getting started takes less than 2 minutes:</p>
            <div style="margin: 2rem 0;">
                <p><strong>1.</strong> Add InterChat to your server using the invite button above</p>
                <p><strong>2.</strong> Run <code>/setup</code> for guided configuration, or use <code>/connect</code> to join your first hub</p>
                <p><strong>3.</strong> Watch your community grow as conversations flow seamlessly across server boundaries</p>
            </div>
            
            <a href="vote" class="epic-button">🗳️ Vote Every 12 Hours</a>
            <a href="https://interchat.tech/docs" class="epic-button">📚 Documentation</a>
            
            <p style="margin-top: 2rem; font-style: italic; color: #B794F6;">
                <strong>Don't forget to vote every 12 hours to support InterChat and unlock premium features!</strong>
            </p>
            
            <p style="font-size: 1.2em; font-weight: bold; margin-top: 1rem;">
                Transform your Discord community today. Your members will thank you for it. ✨
            </p>
        </div>
    </div>
</body>
