/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import type { Message } from 'discord.js';
import type { User as DbUser } from '#src/generated/prisma/client/client.js';
import type HubManager from '#src/managers/HubManager.js';
import { HubReputationService, type ReputationRequirements } from '#src/services/HubReputationService.js';
import { getEmoji } from '#src/utils/EmojiUtils.js';
import Logger from '#src/utils/Logger.js';

export interface ReputationCheckResult {
  passed: boolean;
  reason?: string;
  requiredRP?: number;
  currentRP?: number;
}

export interface ReputationCheckOptions {
  userData: DbUser;
  hub: HubManager;
  attachmentURL?: string | null;
}

export interface ExtendedReputationCheckOptions extends ReputationCheckOptions {
  currentRP: number;
  requirements: ReputationRequirements;
}

/**
 * Optimized function that runs all reputation checks with a single data fetch
 *
 * **Performance Optimization:**
 * - Reduces 12 async calls (6 × 2) to just 2 async calls
 * - Eliminates duplicate database/cache queries for the same data
 * - Significantly improves message processing performance
 *
 * **Before:** Each check function independently fetched:
 * - getUserReputation(userId, hubId)
 * - getHubReputationRequirements(hubId)
 *
 * **After:** Single fetch at the beginning, data passed to all checks
 *
 * @param message The Discord message being checked
 * @param opts Reputation check options containing user data and hub info
 * @returns Array of reputation check results
 */
export async function runOptimizedReputationChecks(
  message: Message<true>,
  opts: ReputationCheckOptions,
): Promise<ReputationCheckResult[]> {
  const reputationService = new HubReputationService();

  // Fetch reputation data once for all checks (PERFORMANCE CRITICAL)
  const [currentRP, requirements] = await Promise.all([
    reputationService.getUserReputation(message.author.id, opts.hub.id),
    reputationService.getHubReputationRequirements(opts.hub.id),
  ]);

  // Create optimized options with pre-fetched data
  const fullOpts: ExtendedReputationCheckOptions = {
    ...opts,
    currentRP,
    requirements,
  };

  // Run all reputation checks with pre-fetched data (no additional async calls)
  const results = await Promise.all([
    checkImageUploadReputation(message, fullOpts),
    checkGifUploadReputation(message, fullOpts),
    checkVideoUploadReputation(message, fullOpts),
    checkStickerReputation(message, fullOpts),
    checkLongMessageReputation(message, fullOpts),
    checkEmbedReputation(message, fullOpts),
  ]);

  return results;
}

/**
 * Checks if user has sufficient reputation for image uploads
 */
export async function checkImageUploadReputation(
  message: Message<true>,
  opts: ExtendedReputationCheckOptions,
): Promise<ReputationCheckResult> {
  // Only check if message has image attachments
  const hasImages = message.attachments.some((attachment) =>
    attachment.contentType?.startsWith('image/') &&
    !attachment.contentType.includes('gif'),
  ) || (opts.attachmentURL && !opts.attachmentURL.includes('.gif'));

  if (!hasImages) {
    return { passed: true };
  }

  if (opts.currentRP < opts.requirements.imageUpload) {
    return {
      passed: false,
      reason: `${getEmoji('x_icon', message.client)} You need **${opts.requirements.imageUpload} RP** to upload images in this hub. You currently have **${opts.currentRP} RP**. Send more messages to earn reputation!`,
      requiredRP: opts.requirements.imageUpload,
      currentRP: opts.currentRP,
    };
  }

  return { passed: true };
}

/**
 * Checks if user has sufficient reputation for GIF uploads
 */
export async function checkGifUploadReputation(
  message: Message<true>,
  opts: ExtendedReputationCheckOptions,
): Promise<ReputationCheckResult> {
  // Check for GIF attachments or GIF URLs
  const hasGifs = message.attachments.some((attachment) =>
    attachment.contentType?.includes('gif'),
  ) || (opts.attachmentURL && opts.attachmentURL.includes('.gif'));

  if (!hasGifs) {
    return { passed: true };
  }

  if (opts.currentRP < opts.requirements.gifUpload) {
    return {
      passed: false,
      reason: `${getEmoji('x_icon', message.client)} You need **${opts.requirements.gifUpload} RP** to upload GIFs in this hub. You currently have **${opts.currentRP} RP**. Keep participating to earn more reputation!`,
      requiredRP: opts.requirements.gifUpload,
      currentRP: opts.currentRP,
    };
  }

  return { passed: true };
}

/**
 * Checks if user has sufficient reputation for video uploads
 */
export async function checkVideoUploadReputation(
  message: Message<true>,
  opts: ExtendedReputationCheckOptions,
): Promise<ReputationCheckResult> {
  // Check for video attachments
  const hasVideos = message.attachments.some((attachment) =>
    attachment.contentType?.startsWith('video/'),
  );

  if (!hasVideos) {
    return { passed: true };
  }

  if (opts.currentRP < opts.requirements.videoUpload) {
    return {
      passed: false,
      reason: `${getEmoji('x_icon', message.client)} You need **${opts.requirements.videoUpload} RP** to upload videos in this hub. You currently have **${opts.currentRP} RP**. Continue being active to build your reputation!`,
      requiredRP: opts.requirements.videoUpload,
      currentRP: opts.currentRP,
    };
  }

  return { passed: true };
}

/**
 * Checks if user has sufficient reputation for stickers
 */
export async function checkStickerReputation(
  message: Message<true>,
  opts: ExtendedReputationCheckOptions,
): Promise<ReputationCheckResult> {
  if (message.stickers.size === 0) {
    return { passed: true };
  }

  if (opts.currentRP < opts.requirements.sticker) {
    return {
      passed: false,
      reason: `${getEmoji('x_icon', message.client)} You need **${opts.requirements.sticker} RP** to send stickers in this hub. You currently have **${opts.currentRP} RP**. Send more messages to earn reputation!`,
      requiredRP: opts.requirements.sticker,
      currentRP: opts.currentRP,
    };
  }

  return { passed: true };
}

/**
 * Checks if user has sufficient reputation for long messages
 */
export async function checkLongMessageReputation(
  message: Message<true>,
  opts: ExtendedReputationCheckOptions,
): Promise<ReputationCheckResult> {
  // Consider messages over 500 characters as "long"
  const LONG_MESSAGE_THRESHOLD = 500;

  if (message.content.length <= LONG_MESSAGE_THRESHOLD) {
    return { passed: true };
  }

  if (opts.currentRP < opts.requirements.longMessage) {
    return {
      passed: false,
      reason: `${getEmoji('x_icon', message.client)} You need **${opts.requirements.longMessage} RP** to send long messages in this hub. You currently have **${opts.currentRP} RP**. Build your reputation with shorter messages first!`,
      requiredRP: opts.requirements.longMessage,
      currentRP: opts.currentRP,
    };
  }

  return { passed: true };
}

/**
 * Checks if user has sufficient reputation for links and formatting (rich content)
 */
export async function checkEmbedReputation(
  message: Message<true>,
  opts: ExtendedReputationCheckOptions,
): Promise<ReputationCheckResult> {
  // Check if message contains links, code blocks, spoilers, or other formatting
  const linkMatches = message.content.match(/https?:\/\/[^\s]+/g);
  const hasRichContent = message.embeds.length > 0 ||
    message.content.includes('```') || // Code blocks
    message.content.includes('||') || // Spoilers
    (linkMatches && linkMatches.length > 1); // Multiple links

  if (!hasRichContent) {
    return { passed: true };
  }

  if (opts.currentRP < opts.requirements.embed) {
    return {
      passed: false,
      reason: `${getEmoji('x_icon', message.client)} You need **${opts.requirements.embed} RP** to send links and formatted content in this hub. You currently have **${opts.currentRP} RP**. Participate more to unlock this feature!`,
      requiredRP: opts.requirements.embed,
      currentRP: opts.currentRP,
    };
  }

  return { passed: true };
}

/**
 * Awards reputation for a successful message
 */
export async function awardMessageReputation(
  message: Message<true>,
  hubId: string,
): Promise<void> {
  try {
    const reputationService = new HubReputationService();
    const newReputation = await reputationService.awardMessageReputation(
      message.author.id,
      hubId,
      message.content.length,
    );

    Logger.debug(
      `Awarded reputation to user ${message.author.id} in hub ${hubId}. New total: ${newReputation} RP`,
    );
  }
  catch (error) {
    Logger.error('Error awarding message reputation:', error);
  }
}

/**
 * Applies reputation penalty for violations
 */
export async function applyReputationPenalty(
  userId: string,
  hubId: string,
  penaltyType: 'AUTO_BLACKLIST' | 'MANUAL_BLACKLIST_TEMP' | 'MANUAL_BLACKLIST_PERM' | 'WARNING',
  moderatorId?: string,
): Promise<void> {
  try {
    const reputationService = new HubReputationService();
    const newReputation = await reputationService.applyPenalty(
      userId,
      hubId,
      penaltyType,
      moderatorId,
    );

    Logger.debug(
      `Applied ${penaltyType} penalty to user ${userId} in hub ${hubId}. New total: ${newReputation} RP`,
    );
  }
  catch (error) {
    Logger.error('Error applying reputation penalty:', error);
  }
}

/**
 * Applies reputation penalty for manual blacklists with automatic temp/perm detection
 */
export async function applyManualBlacklistPenalty(
  userId: string,
  hubId: string,
  expiresAt: Date | null,
  moderatorId?: string,
): Promise<void> {
  // Determine if blacklist is temporary or permanent based on expiration
  const penaltyType = expiresAt ? 'MANUAL_BLACKLIST_TEMP' : 'MANUAL_BLACKLIST_PERM';
  await applyReputationPenalty(userId, hubId, penaltyType, moderatorId);
}

/**
 * All reputation checks that should be run for hub messages
 */
export const reputationChecks = [
  checkImageUploadReputation,
  checkGifUploadReputation,
  checkVideoUploadReputation,
  checkStickerReputation,
  checkLongMessageReputation,
  checkEmbedReputation,
] as const;
