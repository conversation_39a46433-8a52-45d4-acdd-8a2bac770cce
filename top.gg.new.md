<style>
    .interchat-description {
        background: linear-gradient(135deg, #1e1e2e 0%, #2d2d44 50%, #1a1a2e 100%);
        color: #ffffff;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 20px 60px rgba(145, 114, 216, 0.3);
        position: relative;
        overflow: hidden;
        animation: glow 3s ease-in-out infinite alternate;
    }

    /* Epic glow animation */
    @keyframes glow {
        from {
            box-shadow: 0 20px 60px rgba(145, 114, 216, 0.3);
        }

        to {
            box-shadow: 0 25px 80px rgba(145, 114, 216, 0.5);
        }
    }

    /* Animated background particles - scoped */
    .interchat-description::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            radial-gradient(circle at 20% 20%, rgba(145, 114, 216, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 80%, rgba(145, 114, 216, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 40% 60%, rgba(145, 114, 216, 0.05) 0%, transparent 50%);
        animation: float 6s ease-in-out infinite;
        pointer-events: none;
    }

    @keyframes float {

        0%,
        100% {
            transform: translateY(0px) rotate(0deg);
        }

        50% {
            transform: translateY(-10px) rotate(2deg);
        }
    }

    /* Headers with epic styling - scoped to container */
    .interchat-description h1 {
        background: linear-gradient(45deg, #9172D8, #B794F6, #E879F9);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-size: 2.5em;
        font-weight: 900;
        text-align: center;
        margin-bottom: 1rem;
        text-shadow: 0 0 30px rgba(145, 114, 216, 0.5);
        animation: pulse 2s ease-in-out infinite alternate;
        position: relative;
        z-index: 2;
    }

    @keyframes pulse {
        from {
            text-shadow: 0 0 30px rgba(145, 114, 216, 0.5);
        }

        to {
            text-shadow: 0 0 50px rgba(145, 114, 216, 0.8);
        }
    }

    .interchat-description h2 {
        color: #9172D8;
        font-size: 1.8em;
        font-weight: 700;
        margin: 2rem 0 1rem 0;
        position: relative;
        text-transform: uppercase;
        letter-spacing: 2px;
        z-index: 2;
    }

    .interchat-description h2::after {
        content: '';
        position: absolute;
        bottom: -5px;
        left: 0;
        width: 60px;
        height: 3px;
        background: linear-gradient(90deg, #9172D8, transparent);
        border-radius: 2px;
        animation: expand 2s ease-in-out infinite alternate;
    }

    @keyframes expand {
        from {
            width: 60px;
        }

        to {
            width: 120px;
        }
    }

    .interchat-description h3 {
        color: #B794F6;
        font-size: 1.4em;
        font-weight: 600;
        margin: 1.5rem 0 0.8rem 0;
        position: relative;
        z-index: 2;
    }

    /* Epic feature boxes */
    .feature-section {
        background: rgba(145, 114, 216, 0.1);
        border: 2px solid rgba(145, 114, 216, 0.3);
        border-radius: 12px;
        padding: 1.5rem;
        margin: 1.5rem 0;
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        position: relative;
        z-index: 2;
    }

    .feature-section:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(145, 114, 216, 0.4);
        border-color: #9172D8;
    }

    /* Epic bullet points - scoped */
    .interchat-description ul {
        list-style: none;
        padding-left: 0;
        position: relative;
        z-index: 2;
    }

    .interchat-description li {
        position: relative;
        padding-left: 2rem;
        margin: 0.8rem 0;
        transition: all 0.3s ease;
    }

    .interchat-description li::before {
        content: '⚡';
        position: absolute;
        left: 0;
        color: #9172D8;
        font-size: 1.2em;
        animation: spark 1.5s ease-in-out infinite alternate;
    }

    @keyframes spark {
        from {
            transform: scale(1) rotate(0deg);
        }

        to {
            transform: scale(1.2) rotate(10deg);
        }
    }

    .interchat-description li:hover {
        color: #B794F6;
        transform: translateX(10px);
    }

    /* Epic emphasis boxes */
    .emphasis-box {
        background: linear-gradient(135deg, rgba(145, 114, 216, 0.2), rgba(183, 148, 246, 0.1));
        border-left: 4px solid #9172D8;
        padding: 1.2rem;
        margin: 1.5rem 0;
        border-radius: 8px;
        font-weight: 600;
        box-shadow: 0 8px 25px rgba(145, 114, 216, 0.2);
        position: relative;
        z-index: 2;
    }

    /* Epic call-to-action button styling */
    .cta-section {
        text-align: center;
        margin: 2rem 0;
        padding: 2rem;
        background: linear-gradient(135deg, rgba(145, 114, 216, 0.15), rgba(183, 148, 246, 0.1));
        border-radius: 15px;
        border: 2px solid rgba(145, 114, 216, 0.3);
        position: relative;
        z-index: 2;
    }

    .epic-button {
        display: inline-block;
        background: linear-gradient(45deg, #9172D8, #B794F6);
        color: white;
        padding: 1rem 2rem;
        border-radius: 50px;
        text-decoration: none;
        font-weight: bold;
        font-size: 1.1em;
        text-transform: uppercase;
        letter-spacing: 1px;
        box-shadow: 0 10px 30px rgba(145, 114, 216, 0.4);
        transition: all 0.3s ease;
        margin: 0.5rem;
        position: relative;
        overflow: hidden;
    }

    .epic-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .epic-button:hover::before {
        left: 100%;
    }

    .epic-button:hover {
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 15px 40px rgba(145, 114, 216, 0.6);
    }

    /* Premium features highlight */
    .premium-highlight {
        background: linear-gradient(135deg, #FFD700, #FFA500);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-weight: bold;
        text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
        animation: shimmer 2s ease-in-out infinite alternate;
    }

    @keyframes shimmer {
        from {
            opacity: 0.8;
        }

        to {
            opacity: 1;
        }
    }

    /* Strong text styling - scoped */
    .interchat-description strong {
        color: #9172D8;
        font-weight: 700;
    }

    /* Paragraph spacing and styling - scoped */
    .interchat-description p {
        margin: 1rem 0;
        position: relative;
        z-index: 2;
    }

    /* Mobile responsiveness - scoped */
    @media (max-width: 768px) {
        .interchat-description {
            padding: 1.5rem;
            margin: 1rem;
        }

        .interchat-description h1 {
            font-size: 2em;
        }

        .interchat-description h2 {
            font-size: 1.5em;
        }

        .epic-button {
            padding: 0.8rem 1.5rem;
            font-size: 1em;
        }
    }

    /* Remove global scrollbar styling that might affect Top.gg */
    /* Epic scrollbar - only apply if needed */
    /*
        ::-webkit-scrollbar {
            width: 12px;
        }
        
        ::-webkit-scrollbar-track {
            background: #1e1e2e;
        }
        
        ::-webkit-scrollbar-thumb {
            background: linear-gradient(180deg, #9172D8, #B794F6);
            border-radius: 6px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(180deg, #B794F6, #E879F9);
        }
        */
</style>
<div class="interchat-description">
    <h1>InterChat: Where Discord Communities Unite</h1>

    <div class="feature-section">
        <h2>🚀 The Problem Every Growing Community Faces</h2>
        <p>Your Discord community is thriving, but you've hit a wall. Server member limits, multiple related servers, or
            the need to connect with allied communities – Discord's one-server-at-a-time design holds back your
            community's true potential.</p>
    </div>

    <div class="emphasis-box">
        <strong>The Solution: Seamless Cross-Server Communication</strong><br>
        InterChat eliminates Discord server boundaries by creating live communication bridges between servers. Your
        members can chat naturally across multiple servers as if they were all in the same place, without leaving their
        home server or managing multiple memberships.
    </div>

    <h2>⚡ How InterChat Works</h2>

    <div class="feature-section">
        <h3>🌐 Community Hubs: Long-Term Connections</h3>
        <p>Perfect for permanent partnerships and large networks. Create or join themed hubs where multiple servers
            share ongoing conversations:</p>
        <ul>
            <li><strong>Gaming Networks</strong>: Connect your clan's recruitment server, competitive team server, and
                casual gaming server</li>
            <li><strong>Educational Communities</strong>: Link university study groups, subject-specific servers, and
                tutoring communities</li>
            <li><strong>Creator Collectives</strong>: Unite art servers, writing communities, and content creator
                support groups</li>
            <li><strong>Business Ecosystems</strong>: Connect company servers, partner organizations, and industry
                communities</li>
        </ul>
    </div>

    <div class="feature-section">
        <h3>📞 Server Calls: Quick Social Connections</h3>
        <p>Perfect for spontaneous conversations and meeting new people. Instantly connect with another server for:</p>
        <ul>
            <li>Finding active chat partners when your server is quiet</li>
            <li>Breaking the ice with new communities</li>
            <li>Casual conversations and making new friends</li>
            <li>Discovering servers with similar interests</li>
        </ul>
    </div>

    <h2>🎯 Why Communities Choose InterChat</h2>

    <div class="feature-section">
        <p><strong>Effortless Growth</strong>: Scale your community across multiple servers without fragmenting your
            user base or forcing members to join multiple servers.</p>

        <p><strong>Maintained Identity</strong>: Each server keeps its unique culture, rules, and moderation while
            participating in larger conversations.</p>

        <p><strong>Complete Control</strong>: Comprehensive admin dashboard lets you manage connections, monitor
            activity, and customize settings from one central location.</p>
    </div>

    <div class="feature-section">
        <h2 class="premium-highlight">✨ Premium Features</h2>
        <p>Vote for InterChat every 12 hours to unlock premium perks:</p>
        <ul>
            <li>Extended message length limits</li>
            <li>Custom sticker support</li>
            <li>Personalized welcome messages</li>
        </ul>
    </div>

    <div class="emphasis-box">
        <strong>Real Results for Real Communities</strong><br>
        Thousands of Discord communities already use InterChat to break through server limitations and build stronger,
        more connected member experiences. From 50-member hobby groups to networks spanning dozens of servers, InterChat
        scales with your community's ambitions.
    </div>

    <div class="cta-section">
        <h2>🚀 Start Connecting Today</h2>
        <p>Ready to unlock your community's potential? Getting started takes less than 2 minutes:</p>
        <div style="margin: 2rem 0;">
            <p><strong>1.</strong> Add InterChat to your server using the invite button above</p>
            <p><strong>2.</strong> Run <code>/setup</code> for guided configuration, or use <code>/connect</code> to
                join your first hub</p>
            <p><strong>3.</strong> Watch your community grow as conversations flow seamlessly across server boundaries
            </p>
        </div>

        <a href="#" class="epic-button">🗳️ Vote Every 12 Hours</a>
        <a href="#" class="epic-button">📚 Documentation</a>

        <p style="margin-top: 2rem; font-style: italic; color: #B794F6;">
            <strong>Don't forget to vote every 12 hours to support InterChat and unlock premium features!</strong>
        </p>

        <p style="font-size: 1.2em; font-weight: bold; margin-top: 1rem;">
            Transform your Discord community today. Your members will thank you for it. ✨
        </p>
    </div>
</div>